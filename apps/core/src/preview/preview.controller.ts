import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  <PERSON><PERSON>,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '@shared-library/guard/jwt.guard';
import { ApiRequest } from '@shared-library/modules/auth/interfaces/jwt-payload.interface';
import { PreviewService } from './preview.service';
import { Daytona } from '@daytonaio/sdk';

@Controller('preview')
export class PreviewController {
  private readonly logger = new Logger(PreviewController.name);

  constructor(private readonly previewService: PreviewService) {}

  @Post('upload')
  @UseGuards(JwtAuthGuard)
  async createPreview(
    @Body() files: Record<string, string>,
    @Req() req: ApiRequest,
  ) {
    const userId = req.user.userId;
    this.logger.log(`Received preview creation request from user ${userId}`);

    try {
      if (!files || typeof files !== 'object') {
        throw new HttpException(
          'Invalid files provided',
          HttpStatus.BAD_REQUEST,
        );
      }

      const result = await this.previewService.createPreviewForUser({
        files,
        userId,
      });

      this.logger.log(`Preview created successfully: ${result.previewUrl}`);
      this.logger.log(`Preview Logs ${result.runtimeErrors}`);
      return result;
    } catch (error) {
      this.logger.error('Preview creation failed:', error);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        `Preview creation failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health')
  async healthCheck() {
    return this.previewService.healthCheck();
  }

  // =================== NEW MACHINE MANAGEMENT ENDPOINTS ===================

  /**
   * Suspend a machine to save resources
   */
  @Post('suspend/:appName/:machineId')
  async suspendMachine(
    @Param('appName') appName: string,
    @Param('machineId') machineId: string,
  ) {
    this.logger.log(`Suspending machine ${machineId} in app ${appName}`);

    try {
      await this.previewService.suspendMachine({ appName, machineId });
      this.logger.log(`Suspending machine ${machineId} successful`);
      return {
        message: `Machine ${machineId} suspended successfully`,
        appName,
        machineId,
      };
    } catch (error) {
      this.logger.error('Machine suspension failed:', error);
      throw new HttpException(
        `Machine suspension failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Health check and reactivate machine if needed
   */
  @Post('health-check/:appName')
  async healthCheckAndReactivate(@Param('appName') appName: string) {
    this.logger.log(`Health check and reactivation for app ${appName}`);

    try {
      const result =
        await this.previewService.healthCheckAndReactivate(appName);
      return result;
    } catch (error) {
      this.logger.error('Health check and reactivation failed:', error);
      throw new HttpException(
        `Health check failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Cleanup user machine (called on logout) - SINGLE MACHINE APPROACH
   */
  @Delete('cleanup')
  @UseGuards(JwtAuthGuard)
  async cleanupUserMachine(@Req() req: ApiRequest) {
    const userId = req.user.userId;
    this.logger.log(`Cleaning up machine for user ${userId}`);

    try {
      await this.previewService.cleanupUserMachine(userId);
      return {
        message: 'User machine cleaned up successfully',
        userId,
      };
    } catch (error) {
      this.logger.error('User machine cleanup failed:', error);
      throw new HttpException(
        `Machine cleanup failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Create machine for user's existing app
   */
  @Post('create-machine')
  @UseGuards(JwtAuthGuard)
  async createMachineForUser(@Req() req: ApiRequest) {
    const userId = req.user.userId;
    this.logger.log(`Creating machine for user ${userId}`);

    try {
      const result = await this.previewService.createMachineForUser(userId);
      return result;
    } catch (error) {
      this.logger.error('Machine creation failed:', error);
      throw new HttpException(
        `Machine creation failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get logs for a specific machine
   */
  @Get('logs/:appName/:machineId')
  async getMachineLogs(
    @Param('appName') appName: string,
    @Param('machineId') machineId: string,
  ) {
    this.logger.log(`Getting logs for machine ${machineId} in app ${appName}`);

    try {
      const result = await this.previewService.getMachineLogs(
        appName,
        machineId,
      );
      return result;
    } catch (error) {
      this.logger.error('Get machine logs failed:', error);
      throw new HttpException(
        `Get machine logs failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Test Daytona capabilities and speed
   */
  @Post('daytona')
  @UseGuards(JwtAuthGuard)
  async testDaytona(
    @Body() files: Record<string, string>,
    @Req() req: ApiRequest,
  ) {
    const userId = req.user.userId;
    this.logger.log(`Received Daytona test request from user ${userId}`);

    try {
      if (!files || typeof files !== 'object') {
        throw new HttpException(
          'Invalid files provided',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Initialize Daytona SDK
      const daytona = new Daytona();

      // Step 1: Create sandbox
      this.logger.log('Creating Daytona sandbox...');
      const sandbox = await daytona.create({
        snapshot: 'fullstackfox',
        autoStopInterval: 2,
        autoDeleteInterval: 5,
        public: true,
      });

      this.logger.log(`Sandbox created: ${sandbox.id}`);

      // Step 2: Upload multiple files
      this.logger.log('Uploading files to sandbox...');

      // Convert Record<string, string> to the required format
      const uploadFiles = Object.entries(files).map(
        ([destination, content]) => ({
          source: Buffer.from(content),
          destination: destination,
        }),
      );

      await sandbox.fs.uploadFiles(uploadFiles);
      this.logger.log(`Uploaded ${uploadFiles.length} files to sandbox`);

      // Step 3: Get preview link
      this.logger.log('Getting preview link...');
      const previewInfo = await sandbox.getPreviewLink(3000);

      this.logger.log(`Preview link generated: ${previewInfo.url}`);

      return {
        sandboxId: sandbox.id,
        previewUrl: previewInfo.url,
        previewToken: previewInfo.token,
        filesCount: uploadFiles.length,
        provider: 'daytona',
        message: 'Daytona sandbox created successfully',
      };
    } catch (error) {
      this.logger.error('Daytona test failed:', error);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        `Daytona test failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
